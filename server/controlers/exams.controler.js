import User from "../models/user.model.js";
import jwt from "jsonwebtoken";
import { commonErrors, catchAsync } from "../middlewares/error.middleware.js";
import { JWT_EXPIRES_IN, JWT_SECRET } from "../config/env.js";
import { sendInvitationEmail } from "./mailjet.controler.js";
import Analysis from "../models/analysis.model.js";
import Exams from "../models/exams.model.js";
import AnalysisExams from "../models/analysis_exams.model.js";
import { db } from "../database/postgre.js";

export const createExam = catchAsync(async (req, res, next) => {
  try {
    const { patient, tests, allValidated } = req.body;

    // Validate required fields
    if (!patient.ci) {
      throw commonErrors.missingFields(["Cédula de Identidad"]);
    }

    if (!patient.last_name) {
      throw commonErrors.missingFields(["Apellido"]);
    }

    if (!patient.first_name) {
      throw commonErrors.missingFields(["Nombre"]);
    }

    if (!patient.date_birth) {
      throw commonErrors.missingFields(["Fecha de Nacimiento"]);
    }

    if (!patient.email) {
      throw commonErrors.missingFields(["Correo Electrónico"]);
    }

    // Use database transaction
    const result = await db.transaction(async (trx) => {
      // Create the analysis within transaction
      const analysis = await Analysis.createWithTransaction(trx, {
        patient,
        allValidated,
      });

      const analysisId = analysis.id;
      const examIdArray = [];

      // Create all exams within transaction
      for (const testKey in tests) {
        const test = tests[testKey];
        const exam = await Exams.createWithTransaction(trx, {
          tests_values: test.testValues,
          testTypeId: test.testTypeId,
          validated: test.validated,
        });
        examIdArray.push(exam.id);
      }

      // Create all analysis_exams relationships within transaction
      await AnalysisExams.createWithTransaction(trx, analysisId, examIdArray);

      return analysis;
    });

    res.status(201).json({
      status: "success",
      message: "Examen creado con éxito",
      data: {
        analysis: result,
      },
    });
  } catch (error) {
    next(error);
  }
});

export const getExams = catchAsync(async (req, res, next) => {
  try {
    const { page = 1, limit = 10, sortField = 'created_at', sortOrder = 'desc', filters } = req.query;
    const offset = (page - 1) * limit;

    // Build base query for analyses with filters
    let analysisQuery = db("analysis")
      .select(
        "*",
        db.raw("to_char(date_birth, 'YYYY-MM-DD') as date_birth"),
        db.raw("to_char(created_at, 'YYYY-MM-DD') as created_date"),
        db.raw("to_char(created_at, 'HH12:MI AM') as created_time")
      );

    // Apply filters if provided
    if (filters) {
      const parsedFilters = JSON.parse(filters);
      Object.entries(parsedFilters).forEach(([field, value]) => {
        if (value) {
          analysisQuery = analysisQuery.whereILike(field, `%${value}%`);
        }
      });
    }

    // Get total count for pagination
    const totalCountQuery = analysisQuery.clone().clearSelect().count('* as count').first();

    // Apply sorting and pagination
    const analyses = await analysisQuery
      .orderBy(sortField, sortOrder)
      .limit(limit)
      .offset(offset);

    // Get analysis IDs for the current page
    const analysisIds = analyses.map(a => a.id);

    // Get all exams for these analyses
    const examsData = analysisIds.length > 0 ? await db("analysis_exams")
      .join("exams", "analysis_exams.id_exam", "exams.id")
      .join("examination_types", "exams.examination_type_id", "examination_types.id")
      .whereIn("analysis_exams.analysis_id", analysisIds)
      .select(
        "analysis_exams.analysis_id",
        "exams.*",
        "examination_types.name as examination_type_name"
      ) : [];

    // Helper functions
    const calculateAge = (dateOfBirth) => {
      const dob = new Date(dateOfBirth);
      const today = new Date();
      let age = today.getFullYear() - dob.getFullYear();
      const m = today.getMonth() - dob.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
        age--;
      }
      return age;
    };

    const safeJsonParse = (jsonString) => {
      try {
        if (typeof jsonString === 'object' && jsonString !== null) {
          return jsonString;
        }
        if (typeof jsonString === 'string') {
          return JSON.parse(jsonString);
        }
        return {};
      } catch (error) {
        console.error('JSON parse error:', error, 'Input:', jsonString);
        return {};
      }
    };

    // Group exams by analysis_id
    const examsByAnalysis = examsData.reduce((acc, exam) => {
      if (!acc[exam.analysis_id]) acc[exam.analysis_id] = [];
      acc[exam.analysis_id].push(exam);
      return acc;
    }, {});

    // Combine data
    const result = analyses.map(analysis => ({
      id: analysis.id,
      patient: {
        ci: analysis.ci,
        first_name: analysis.first_name,
        last_name: analysis.last_name,
        date_birth: analysis.date_birth,
        email: analysis.email,
        phone_number: analysis.phone_number,
        address: analysis.address,
        sex: analysis.sex,
        patient_id: null
      },
      allValidated: analysis.allValidated,
      created_date: analysis.created_date,
      created_time: analysis.created_time,
      age: calculateAge(analysis.date_birth),
      tests: (examsByAnalysis[analysis.id] || []).reduce((acc, exam) => {
        acc[exam.examination_type_id] = {
          testValues: safeJsonParse(exam.tests_values),
          testTypeName: exam.examination_type_name,
          testTypeId: exam.examination_type_id,
          validated: exam.validated
        };
        return acc;
      }, {})
    }));

    const { count } = await totalCountQuery;

    res.status(200).json({
      status: "success",
      data: {
        exams: result,
        totalCount: parseInt(count),
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / limit)
      },
    });
  } catch (error) {
    next(error);
  }
});

export const findExamById = catchAsync(async (req, res, next) => {
  try {
    console.log(req.params.id);
    const exam = await Exam.findById(req.params.id);
    if (!exam) {
      throw commonErrors.notFound("Exam");
    }
    res.status(200).json({
      status: "success",
      data: {
        exam,
      },
    });
  } catch (error) {
    next(error);
  }
});

export const updateExam = catchAsync(async (req, res, next) => {
  try {
    const exam = await Exam.findById(req.params.id);
    if (!exam) {
      throw commonErrors.notFound("Exam");
    }
    await Exam.update(req.params.id, req.body);
    res.status(200).json({
      status: "success",
      message: "Examen actualizado con éxito",
    });
  } catch (error) {
    next(error);
  }
});

export const deleteExam = catchAsync(async (req, res, next) => {
  try {
    const exam = await Exam.findById(req.params.id);
    if (!exam) {
      throw commonErrors.notFound("Exam");
    }
    await Exam.delete(req.params.id);
    res.status(200).json({
      status: "success",
      message: "Examen eliminado con éxito",
    });
  } catch (error) {
    next(error);
  }
});

export const validateExam = catchAsync(async (req, res, next) => {
  try {
    const { id } = req.body;
    if (!id) {
      throw commonErrors.missingFields(["id"]);
    }
    await Exam.updateById(id, { allValidated: true });
    res.status(200).json({
      status: "success",
      message: "Examen validado con éxito",
    });
  } catch (error) {
    next(error);
  }
});
