import React from 'react';

const FastInput = React.memo(({
  type = 'text',
  name,
  label,
  value,
  onChange,
  error,
  helperText,
  required = false,
  disabled = false,
  placeholder,
  className = '',
  size = 'medium',
  ...props
}) => {
  const inputId = `input-${name}`;
  
  const baseInputClasses = `
    w-full px-3 py-2 border rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 
    ${error 
      ? 'border-red-500 focus:border-red-500 focus:ring-red-200' 
      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
    }
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
    ${size === 'small' ? 'py-1.5 text-sm' : 'py-2'}
  `.trim().replace(/\s+/g, ' ');

  const labelClasses = `
    block text-sm font-medium mb-1
    ${error ? 'text-red-700' : 'text-gray-700'}
    ${disabled ? 'text-gray-500' : ''}
  `.trim().replace(/\s+/g, ' ');

  return (
    <div className={`${className}`}>
      {label && (
        <label htmlFor={inputId} className={labelClasses}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <input
        id={inputId}
        type={type}
        name={name}
        value={value || ''}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        className={baseInputClasses}
        {...props}
      />
      
      {(error || helperText) && (
        <p className={`mt-1 text-xs ${error ? 'text-red-600' : 'text-gray-500'}`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
});

FastInput.displayName = 'FastInput';

export default FastInput;
