import { TextField, Checkbox, FormControlLabel, FormHelperText } from '@mui/material';

export default function FormField({
  type = 'text',
  name,
  label,
  value,
  onChange,
  error,
  helperText,
  required = false,
  disabled = false,
  placeholder,
  fullWidth = true,
  variant = 'outlined',
  className = '',
  ...props
}) {
  if (type === 'checkbox') {
    return (
      <div className={className + "mb-0"}>
        <FormControlLabel
          control={
            <Checkbox
              name={name}
              checked={value || false}
              onChange={onChange}
              disabled={disabled}
              color="primary"
              {...props}
            />
          }
          label={label }
        />
        {error && <FormHelperText error>{error}</FormHelperText>}
        {helperText && !error && <FormHelperText className='relative -top-2' >{helperText}</FormHelperText>}
      </div>
    );
  }

  return (
    <div className={className}>
      <TextField
        type={type}
        name={name}
        label={label}
        value={value || ''}
        onChange={onChange}
        error={!!error}
        helperText={error || helperText}
        required={required}
        disabled={disabled}
        placeholder={placeholder}
        fullWidth={fullWidth}
        variant={variant}
        {...props}
      />
    </div>
  );
}
