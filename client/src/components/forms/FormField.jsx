import {
  Checkbox,
  FormHelperText,
} from "@mui/material";
import React from "react"; // Import React for React.memo
import FastInput from './FastInput';
import FastSelect from './FastSelect';

const FormField = React.memo(function FormField({

  type = "text",
  name,
  label,
  value,
  onChange,
  error,
  helperText,
  required = false,
  disabled = false,
  placeholder,
  fullWidth = true,
  variant = "outlined",
  className = "",
  unit,
  ...props
}) {
  if (type === "checkbox") {
    return (
      <div className={`flex items-start gap-3 ${className}`}>
        <Checkbox
          name={name}
          checked={value || false}
          onChange={onChange}
          disabled={disabled}
          color="primary"
          className="mt-1"
          {...props}
        />
        <div className="flex flex-col">
          <label className="text-sm font-medium text-gray-700 ">
            {label || name}
          </label>
          {helperText && !error && (
            <FormHelperText className="mt-0 text-xs text-gray-500">
              {helperText}
            </FormHelperText>
          )}
          {error && (
            <FormHelperText error className="mt-0">
              {error}
            </FormHelperText>
          )}
        </div>
      </div>
    );
  } else if (type === "select") {
    return (
      <FastSelect
        name={name}
        label={label}
        value={value}
        onChange={onChange}
        options={props.options || []}
        error={error}
        helperText={helperText}
        required={required}
        disabled={disabled}
        className={className}
        size="small"
        {...props}
      />
    );
  }

  return (
    <FastInput
      type={type}
      name={name}
      label={label}
      value={
        type === "date" && typeof value === "string"
          ? value.split("T")[0]
          : value || ""
      }
      onChange={onChange}
      error={error}
      helperText={helperText}
      required={required}
      disabled={disabled}
      placeholder={placeholder}
      className={className}
      size="small"
      onWheel={
        type === "number"
          ? (e) => e.target.blur() // 👈 disables scroll-change
          : undefined
      }
      {...props}
    />
  );
});

export default FormField;
