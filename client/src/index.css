@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Exo+2:ital,wght@1,100..900&family=Inter:ital,opsz,wght@1,14..32,100..900&display=swap');

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
body, 
.MuiDataGrid-root, 
.MuiDataGrid-columnHeader, 
.MuiDataGrid-cell, 
.MuiDataGrid-toolbarContainer {
  font-family: "Inter", sans-serif !important;
  font-optical-sizing: auto;
}

h1, h2, h3, h4, h5 {
  font-family: "Exo 2", sans-serif !important;
  font-optical-sizing: auto;
}
.font-exo2 {
  font-family: "Exo 2", sans-serif !important;
  font-optical-sizing: auto;
}
.font-inter {
  font-family: "Inter", sans-serif !important;
  font-optical-sizing: auto;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-slideOut {
  animation: slideOut 0.3s ease-in forwards;
}

.activeLink:after {
  content: "";
  position: absolute;
  right: -16px;
  top: -18.5px;
  width: 28px;
  height: 83px;
  /* background-color: #397373; */
  background-image: url("./assets/linkCurve.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right center;
  border-radius: 0 2px 2px 0;
}
.MuiDataGrid-root {
  border-radius: 20px !important;
  overflow: hidden !important;
  border-color: white !important;
  box-shadow: 1px 1px 8px rgba(61, 61, 61, 0.13);
}
.MuiDataGrid-row, .MuiDataGrid-cell, .MuiDataGrid-virtualScrollerContent .css-aymtem-MuiDataGrid-virtualScrollerContent{
  border-color: white !important;
}

/* AG Grid - Remove border and add light shadow */
.ag-grid-no-border .ag-root-wrapper {
  border: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03) !important;
  border-radius: 8px !important;
}

.ag-grid-no-border .ag-root {
  border: none !important;
}
.kave-btn {
  position: relative;
  border: solid 1px #6595BF;
  background: none;
  color: #011140;
  font-family: "Exo 2", sans-serif !important;
  text-transform: uppercase;
  font-weight: normal;
  letter-spacing: 1.8px;
  width: 170px;
  border-radius: 5px;
  height: 40px;
  padding: 0;
  transition: all 0.25s ease;
  outline: none;
}

.kave-btn:before {
  content: "";
  position: absolute;
  top: 0;
  left: 10%;
  width: 60px;
  height: 1px;
  transform: translateY(-1px);
  background: #6595BF;
  transition: all 0.25s ease;
}

.kave-btn:after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 10%;
  width: 60px;
  height: 1px;
  transform: translateY(1px);
  background: #6595BF;
  transition: all 0.25s ease;
}

.kave-btn:hover {
  box-shadow: 1px 1px 8px rgba(0, 214, 252, 0.3);
  color: #6595BF;
  text-shadow: 0 0 8px rgba(0, 214, 252, 0.4);
}

.kave-btn:hover:before {
  left: 0;
  width: 20px;
}

.kave-btn:hover:after {
  right: 0;
  width: 20px;
}

.kave-btn:hover .kave-line:before {
  bottom: 0;
}

.kave-btn:hover .kave-line:after {
  top: 0;
}

.kave-line:before {
  content: "";
  position: absolute;
  bottom: 30%;
  right: 0;
  width: 1px;
  height: 20px;
  transform: translateX(1px);
  background: rgba(0, 214, 252, 1);
  transition: all 0.25s ease;
}

.kave-line:after {
  content: "";
  position: absolute;
  top: 30%;
  left: 0;
  width: 1px;
  height: 20px;
  transform: translateX(-1px);
  background: rgba(0, 214, 252, 1);
  transition: all 0.25s ease;
}