@tailwind base;
@tailwind components;
@tailwind utilities;

@import url("https://fonts.googleapis.com/css2?family=Exo+2:ital,wght@1,100..900&family=Inter:ital,opsz,wght@1,14..32,100..900&display=swap");

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
body,
.MuiDataGrid-root,
.MuiDataGrid-columnHeader,
.MuiDataGrid-cell,
.MuiDataGrid-toolbarContainer {
  font-family: "Inter", sans-serif !important;
  font-optical-sizing: auto;
}
body {
  scrollbar-color: #dcdddd transparent;
  scrollbar-width: thin;
  border-radius: 20px;
}

/* Remove shadow from MaterialReactTable paper */
.MuiPaper-root {
  --paper-shadow: none !important;
  box-shadow: 8px 8px 30px #e0e0e0, -8px -8px 50px #ffffff !important;
  border-radius: 20px !important;
}
td.MuiTableCell-root {
  border-color: rgb(247, 247, 247) !important;
}
.neuphormism {
  border-radius: 40px;
  background: #f7f7f7;
  box-shadow: 12px 12px 25px #d3d3d3ec, -12px -12px 15px #ffffff;
}

h1,
h2,
h3,
h4,
h5 {
  font-family: "Exo 2", sans-serif !important;
  font-optical-sizing: auto;
}
.font-exo2 {
  font-family: "Exo 2", sans-serif !important;
  font-optical-sizing: auto;
}
.font-inter {
  font-family: "Inter", sans-serif !important;
  font-optical-sizing: auto;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-slideOut {
  animation: slideOut 0.3s ease-in forwards;
}

.activeLink:after {
  content: "";
  position: absolute;
  right: -17px;
  top: -18.5px;
  width: 28px;
  height: 83px;
  /* background-color: #397373; */
  background-image: url("./assets/linkCurve.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right center;
  border-radius: 0 2px 2px 0;
}
.MuiDataGrid-root {
  border-radius: 20px !important;
  overflow: hidden !important;
  border-color: white !important;
  box-shadow: 1px 1px 8px rgba(61, 61, 61, 0.13);
}
.MuiDataGrid-row,
.MuiDataGrid-cell,
.MuiDataGrid-virtualScrollerContent
  .css-aymtem-MuiDataGrid-virtualScrollerContent {
  border-color: white !important;
}

/* AG Grid - Remove border and add light shadow */
.ag-grid-no-border .ag-root-wrapper {
  border: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03) !important;
  border-radius: 8px !important;
}

.ag-grid-no-border .ag-root {
  border: none !important;
}

/* AG Grid - Darker header */
.ag-grid-no-border .ag-header {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
}

.ag-grid-no-border .ag-header-cell {
  background-color: #f8f9fa !important;
  font-weight: 600 !important;
  color: #495057 !important;
}
/* From Uiverse.io by virus231 */
.button {
  background: linear-gradient(140.14deg, #59d6df 15.05%, #0554af 114.99%)
      padding-box,
    linear-gradient(142.51deg, #59d6df 8.65%, #0554af 88.82%) border-box;
  border-radius: 7px;
  border: 2px solid transparent;

  text-shadow: 1px 1px 1px #00000040;
  box-shadow: 4px 4px 10px 0px #4509003f;

  padding: 10px 40px;
  line-height: 20px;
  cursor: pointer;
  transition: all 0.3s;
  color: white;
  font-size: 18px;
  font-weight: 500;
}
@media (max-width:500px) {
  .button {
    font-size: 13px;
    padding: 5px 20px;
  }
  
}

.button:hover {
  box-shadow: none;
  opacity: 80%;
}

/* Login page */
:root {
  --color-bg1: rgb(108, 0, 162);
  --color-bg2: rgb(0, 17, 82);
  --color1: 18, 113, 255;
  --color2: 221, 74, 255;
  --color3: 100, 220, 255;
  --color4: 200, 50, 50;
  --color5: 180, 180, 50;
  --color-interactive: 140, 100, 255;
  --circle-size: 80%;
  --blending: hard-light;
}

@keyframes moveInCircle {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes moveVertical {
  0% {
    transform: translateY(-50%);
  }
  50% {
    transform: translateY(50%);
  }
  100% {
    transform: translateY(-50%);
  }
}
@keyframes moveHorizontal {
  0% {
    transform: translateX(-50%) translateY(-10%);
  }
  50% {
    transform: translateX(50%) translateY(10%);
  }
  100% {
    transform: translateX(-50%) translateY(-10%);
  }
}
.gradient-bg {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(40deg, var(--color-bg1), var(--color-bg2));
  top: 0;
  left: 0;
}
.gradient-bg svg {
  display: none;
}
.gradient-bg .gradients-container {
  filter: url(#goo) blur(20px); /* Reduced from 40px */
  width: 100%;
  height: 100%;
  z-index: -9999999999999;
}
/* Optimized animations with will-change and transform3d */
.gradient-bg .g1 {
  position: absolute;
  background: radial-gradient(
      circle at center,
      rgba(var(--color1), 0.6) 0,
      rgba(var(--color1), 0) 50%
    )
    no-repeat;
  mix-blend-mode: var(--blending);
  width: var(--circle-size);
  height: var(--circle-size);
  top: calc(50% - var(--circle-size) / 2);
  left: calc(50% - var(--circle-size) / 2);
  transform-origin: center center;
  animation: moveVertical 30s ease infinite;
  will-change: transform;
  transform: translate3d(0, 0, 0); /* Force hardware acceleration */
}
.gradient-bg .g2 {
  position: absolute;
  background: radial-gradient(
      circle at center,
      rgba(var(--color2), 0.6) 0,
      rgba(var(--color2), 0) 50%
    )
    no-repeat;
  mix-blend-mode: var(--blending);
  width: var(--circle-size);
  height: var(--circle-size);
  top: calc(50% - var(--circle-size) / 2);
  left: calc(50% - var(--circle-size) / 2);
  transform-origin: calc(50% - 400px);
  animation: moveInCircle 20s reverse infinite;
  will-change: transform;
  transform: translate3d(0, 0, 0);
}
.gradient-bg .g3 {
  position: absolute;
  background: radial-gradient(
      circle at center,
      rgba(var(--color3), 0.8) 0,
      rgba(var(--color3), 0) 50%
    )
    no-repeat;
  mix-blend-mode: var(--blending);
  width: var(--circle-size);
  height: var(--circle-size);
  top: calc(50% - var(--circle-size) / 2 + 200px);
  left: calc(50% - var(--circle-size) / 2 - 500px);
  transform-origin: calc(50% + 400px);
  animation: moveInCircle 40s linear infinite;
  opacity: 1;
}
.gradient-bg .g4 {
  position: absolute;
  background: radial-gradient(
      circle at center,
      rgba(var(--color4), 0.8) 0,
      rgba(var(--color4), 0) 50%
    )
    no-repeat;
  mix-blend-mode: var(--blending);
  width: var(--circle-size);
  height: var(--circle-size);
  top: calc(50% - var(--circle-size) / 2);
  left: calc(50% - var(--circle-size) / 2);
  transform-origin: calc(50% - 200px);
  animation: moveHorizontal 40s ease infinite;
  opacity: 0.7;
}
.gradient-bg .g5 {
  position: absolute;
  background: radial-gradient(
      circle at center,
      rgba(var(--color5), 0.8) 0,
      rgba(var(--color5), 0) 50%
    )
    no-repeat;
  mix-blend-mode: var(--blending);
  width: calc(var(--circle-size) * 2);
  height: calc(var(--circle-size) * 2);
  top: calc(50% - var(--circle-size));
  left: calc(50% - var(--circle-size));
  transform-origin: calc(50% - 800px) calc(50% + 200px);
  animation: moveInCircle 20s ease infinite;
  opacity: 1;
}
/* Optimize interactive element */
.gradient-bg .interactive {
  position: absolute;
  background: radial-gradient(
      circle at center,
      rgba(var(--color-interactive), 0.5) 0,
      rgba(var(--color-interactive), 0) 50%
    )
    no-repeat;
  mix-blend-mode: var(--blending);
  width: 80%; /* Reduced size */
  height: 80%;
  top: -40%;
  left: -40%;
  opacity: 0.5; /* Reduced opacity */
  will-change: transform;
  transform: translate3d(0, 0, 0);
}

.ellipse {
  clip-path: ellipse(40% 50% at 50% 50%);
}
