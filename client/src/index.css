@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Exo+2:ital,wght@1,100..900&family=Inter:ital,opsz,wght@1,14..32,100..900&display=swap');

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
body, 
.MuiDataGrid-root, 
.MuiDataGrid-columnHeader, 
.MuiDataGrid-cell, 
.MuiDataGrid-toolbarContainer {
  font-family: "Inter", sans-serif !important;
  font-optical-sizing: auto;
}

h1, h2, h3, h4, h5 {
  font-family: "Exo 2", sans-serif !important;
  font-optical-sizing: auto;
}
.font-exo2 {
  font-family: "Exo 2", sans-serif !important;
  font-optical-sizing: auto;
}
.font-inter {
  font-family: "Inter", sans-serif !important;
  font-optical-sizing: auto;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-slideOut {
  animation: slideOut 0.3s ease-in forwards;
}

.activeLink:after {
  content: "";
  position: absolute;
  right: -17px;
  top: -18.5px;
  width: 28px;
  height: 83px;
  /* background-color: #397373; */
  background-image: url("./assets/linkCurve.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right center;
  border-radius: 0 2px 2px 0;
}
.MuiDataGrid-root {
  border-radius: 20px !important;
  overflow: hidden !important;
  border-color: white !important;
  box-shadow: 1px 1px 8px rgba(61, 61, 61, 0.13);
}
.MuiDataGrid-row, .MuiDataGrid-cell, .MuiDataGrid-virtualScrollerContent .css-aymtem-MuiDataGrid-virtualScrollerContent{
  border-color: white !important;
}

/* AG Grid - Remove border and add light shadow */
.ag-grid-no-border .ag-root-wrapper {
  border: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03) !important;
  border-radius: 8px !important;
}

.ag-grid-no-border .ag-root {
  border: none !important;
}

/* AG Grid - Darker header */
.ag-grid-no-border .ag-header {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
}

.ag-grid-no-border .ag-header-cell {
  background-color: #f8f9fa !important;
  font-weight: 600 !important;
  color: #495057 !important;
}
/* From Uiverse.io by virus231 */ 
.button {
  background: linear-gradient(140.14deg, #59d6df 15.05%, #0554af 114.99%)
      padding-box,
    linear-gradient(142.51deg, #59d6df 8.65%, #0554af 88.82%) border-box;
  border-radius: 7px;
  border: 2px solid transparent;

  text-shadow: 1px 1px 1px #00000040;
  box-shadow: 4px 4px 10px 0px #4509003f;

  padding: 10px 40px;
  line-height: 20px;
  cursor: pointer;
  transition: all 0.3s;
  color: white;
  font-size: 18px;
  font-weight: 500;
}

.button:hover {
  box-shadow: none;
  opacity: 80%;
}
