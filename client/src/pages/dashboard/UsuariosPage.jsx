import React, { useState, useEffect, useCallback } from "react";
// Eliminar estas importaciones de CSS
// import "ag-grid-community/styles/ag-grid.css";
// import "ag-grid-community/styles/ag-theme-alpine.css";
import { Link } from "react-router-dom";
import { authAPI, usersAPI } from "../../services/api";
import { Icon } from "@iconify/react";
import FuturisticButton from "../../components/FuturisticButton";
import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";

ModuleRegistry.registerModules([AllCommunityModule]);
// En las versiones más recientes, no necesitas registrar módulos para funcionalidades básicas
// La versión Community ya incluye el ClientSideRowModel por defecto

// Función reutilizable para crear operadores de filtro para columnas de texto

export default function Page() {

  const [colDefs, setColDefs] = useState([
    { field: "id", headerName: "ID", width: 65, filter: 'agNumberColumnFilter' },
    { field: "name", headerName: "Nombre", width: 150, type: "string", filter: 'agTextColumnFilter' },
    { field: "last_name", headerName: "Apellido", width: 150, type: "string", filter: 'agTextColumnFilter' },
    { field: "email", headerName: "Correo Electrónico", width: 200, type: "string", filter: 'agTextColumnFilter' },
    { field: "allow_validate_exam", headerName: "Puede Validar", type: "boolean", width: 130, filter: 'agSetColumnFilter' },
    { field: "allow_handle_users", headerName: "Gestión de Usuarios", type: "boolean", width: 180, filter: 'agSetColumnFilter' },
  ]);
  const [rowData, setRowData] = useState([]);
  const [pageSize] = useState(10);
  const [rowCount, setRowCount] = useState(null);
  const [gridApi, setGridApi] = useState(null);

  const fetchData = useCallback(async () => {
    try {
      const res = await usersAPI.getAllUsers();
      console.log(res.data);
      setRowData(res.data.users);
    } catch (e) {
      console.error("Failed to fetch data", e);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);


  
  return (
    <div style={{ height: 580, width: "100%" }}>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Gestión de Usuarios</h1>
        <FuturisticButton>
          <Link to="/dashboard/usuarios/crear">Crear usuario</Link>
        </FuturisticButton>
      </div>

      <div className="ag-theme-alpine" style={{ height: 600 }}>
        <AgGridReact
          columnDefs={colDefs}
          rowData={rowData}
          
        />
      </div>
    </div>
  );
}
