import React, { useState, useEffect, useCallback, useMemo } from "react";
import { examinationTypesAPI, examsAPI } from "../../services/api";
import externalApi from "../../services/saludfalcon.api";
import { Icon } from "@iconify/react";
import Modal from "../../components/Modal";
import FuturisticButton from "../../components/FuturisticButton";
import FormField from "../../components/forms/FormField";
import { CircularProgress } from "@mui/material";
import { useFeedback } from "../../context/FeedbackContext";
import PrintPage from "../../components/PrintableExamResult";
import { MaterialReactTable } from "material-react-table";

import debounce from "lodash.debounce";
import axios from "axios";

// Memoized component for test fields to prevent unnecessary re-renders
const MemoizedTestField = React.memo(
  ({ field, value, onChange, testKey, fieldName }) => {
    const handleChange = useCallback(
      (e) => {
        onChange(testKey, e);
      },
      [onChange, testKey]
    );

    return (
      <FormField
        key={fieldName}
        {...field}
        value={value || ""}
        onChange={handleChange}
      />
    );
  }
);

export default function Page() {
  const [loading, setLoading] = useState(false);
  const { showError, showSuccess } = useFeedback();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [examinationTypes, setExaminationTypes] = useState([]);

  // Form configuration for ReusableForm
  const patientFormFields = useMemo(() => [
    {
      name: "ci",
      label: "Cédula de Identidad",
      type: "text",
      required: true,
      className: "col-span-1",
    },
    {
      name: "first_name",
      label: "Nombre",
      type: "text",
      required: true,
      className: "col-span-1",
    },
    {
      name: "last_name",
      label: "Apellido",
      type: "text",
      required: true,
      className: "col-span-1",
    },
    {
      name: "date_birth",
      label: "Fecha de Nacimiento",
      type: "date",
      required: true,
      className: "col-span-1",
    },
    {
      name: "email",
      label: "Correo Electrónico",
      type: "email",
      required: true,
      className: "col-span-1",
    },
    {
      name: "phone_number",
      label: "Teléfono",
      type: "text",
      required: true,
      className: "col-span-1",
    },
    {
      name: "address",
      label: "Dirección",
      type: "text",
      required: true,
      className: "col-span-1",
    },
    {
      name: "sex",
      label: "Género",
      type: "select",
      options: [
        { value: "Masculino", label: "Masculino" },
        { value: "Femenino", label: "Femenino" },
      ],
      required: true,
      className: "col-span-1",
    },
  ]);
  const defaultFormData = {
    patient: {
      ci: "",
      first_name: "",
      last_name: "",
      date_birth: "",
      email: "",
      phone_number: "",
      address: "",
      sex: "",
      patient_id: null,
    },
    allValidated: false,
    tests: {},
  };

  const [formData, setFormData] = useState(structuredClone(defaultFormData));
  const [submitString, setSubmitString] = useState("Registrar");

  console.log({ formData });

  const onSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      console.log({ formData });

      // Prepare both requests
      const internalRequest =
        submitString === "Actualizar"
          ? examsAPI.updateExam(formData.id, formData)
          : examsAPI.createExam(formData);

      const externalRequest =
        formData.patient.patient_id === null
          ? externalApi.post("/patients", {
              // Map your formData to the external API's expected format
              id: formData.patient.patient_id,
              ...formData.patient,
              name: formData.patient.first_name,
            })
          : Promise.resolve({ success: true, skipped: true });

      // Execute both requests in parallel
      const [internalResponse, externalResponse] = await Promise.all([
        internalRequest,
        externalRequest.catch((e) => {
          console.error("External API failed (non-critical):", e);
          return { success: false, error: e };
        }),
      ]);

      // Handle success
      if (submitString === "Actualizar") {
        setSubmitString("Registrar");
      }

      showSuccess("Operación completada con éxito");
      setFormData(structuredClone(defaultFormData));
      setIsModalOpen(false);
      fetchData();

      // Optional: Log external API result
      if (!externalResponse.success) {
        console.warn("External system update failed (non-critical)");
        // You could show a non-blocking warning here if needed
      }
    } catch (error) {
      // This will only catch errors from the internal API
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Error en el sistema principal";
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const columns = useMemo(
    () => [
      {
        accessorKey: "id",
        header: "Cód",
        size: 60,
      },
      {
        accessorKey: "patient.first_name",
        header: "Nombre",
        size: 110,
        filterFn: "includesString",
      },
      {
        accessorKey: "patient.last_name",
        header: "Apellido",
        size: 120,
        filterFn: "includesString",
      },
      {
        accessorKey: "age",
        header: "Edad",
        size: 83,
        filterFn: "between",
      },
      {
        accessorKey: "patient.ci",
        header: "C.I",
        size: 100,
        filterFn: "includesString",
      },

      {
        accessorKey: "created_date",
        header: "Fecha",
        size: 125,
        filterFn: "equals",
        Cell: ({ cell }) => {
          const value = cell.getValue();
          return new Date(value).toLocaleDateString();
        },
      },
      {
        accessorKey: "created_time",
        header: "Hora",
        size: 100,
        filterFn: "includesString",
      },
      {
        accessorKey: "allValidated",
        header: "Validado",
        size: 100,
        filterFn: "equals",
        Cell: ({ cell }) => (cell.getValue() ? "Sí" : "No"),
      },
      {
        accessorKey: "validated",
        header: "Recibido",
        id: "recibido",
        size: 90,
        Cell: ({ cell }) =>
          cell.getValue() ? (
            <Icon icon="bitcoin-icons:check-outline" width={20} height={20} />
          ) : null,
        enableColumnFilter: false,
        enableSorting: false,
      },
      {
        header: "Acciones",
        id: "actions",
        size: 220,
        enableColumnFilter: false,
        enableSorting: false,
        Cell: ({ row }) => {
          const data = row.original;
          return (
            <div className="flex gap-2 justify-center items-center">
              <button
                onClick={() => {
                  setIsModalOpen(true);
                  setFormData({
                    patient: data.patient,
                    id: data.id,
                    allValidated: data.validated,
                    tests: data.tests,
                  });
                  setSubmitString("Actualizar");
                }}
                title="Editar"
              >
                <Icon icon="hugeicons:edit-02" width={20} height={20} />
              </button>

              <PrintPage data={data} />

              <button
                title="Enviar mensaje"
                className="mx-1 p-1 hover:bg-blue-100 rounded-full"
              >
                <Icon
                  icon="carbon:send-alt"
                  className="w-6 h-6 text-gray-500"
                />
              </button>

              <button
                onClick={() => handleDelete(data.id)}
                title="Eliminar"
                className="ml-auto"
              >
                <Icon
                  icon="heroicons:trash"
                  className="w-5 h-5 text-gray-500"
                />
              </button>
            </div>
          );
        },
      },
    ],
    []
  );

  const handleDelete = async (id) => {
    try {
      if (!window.confirm("¿Está seguro de eliminar este examen?")) {
        return;
      }
      await examsAPI.deleteExam(id);
      showSuccess("Examen eliminado con éxito");
      fetchData();
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || error.message || "An error occurred";
      showError(errorMessage);
    }

    console.log("Delete exam:", exam.id);
    // Call your delete API or show a confirmation dialog
  };

  const [data, setData] = useState([]);
  const [rowCount, setRowCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Server-side state
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 25 });
  const [sorting, setSorting] = useState([]);
  const [columnFilters, setColumnFilters] = useState([]);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const res = await examsAPI.getExams({
        page: pagination.pageIndex + 1,
        limit: pagination.pageSize,
        sortField: sorting[0]?.id || "id",
        sortOrder: sorting[0]?.desc ? "desc" : "asc",
        filters: JSON.stringify(
          columnFilters.reduce((acc, curr) => {
            acc[curr.id] = curr.value;
            return acc;
          }, {})
        ),
      });
      console.log({ res });
      setData(res.data.exams);
      setRowCount(res.data.totalCount);
    } catch (e) {
      console.error("Failed to fetch data", e);
    }
    setIsLoading(false);
  }, [pagination, sorting, columnFilters]);

  const getExaminationTypes = useCallback(async () => {
    try {
      const res = await examinationTypesAPI.getExaminationTypes();
      console.log(res.data);
      setExaminationTypes(res.data.examinationTypes);
    } catch (e) {
      console.error("Failed to fetch data", e);
    }
  }, []);

  useEffect(() => {
    getExaminationTypes();
  }, [getExaminationTypes]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Debounced version of setFormData for better performance
  const debouncedSetFormData = useMemo(
    () =>
      debounce((updateFn) => {
        setFormData(updateFn);
      }, 50),
    []
  );

  const handleTestInputChange = useCallback((examination_type_id, event) => {
    const { name, value } = event.target;

    // Use immediate update for better UX, but debounce heavy operations
    setFormData((prev) => {
      // Early return if value hasn't changed
      if (
        prev.tests?.[examination_type_id]?.testValues?.[name]?.value === value
      ) {
        return prev;
      }

      return {
        ...prev,
        tests: {
          ...prev.tests,
          [examination_type_id]: {
            ...prev.tests[examination_type_id],
            testValues: {
              ...prev.tests[examination_type_id].testValues,
              [name]: {
                ...prev.tests[examination_type_id].testValues[name],
                value,
              },
            },
          },
        },
      };
    });
  }, []);

  const handlePatientInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      patient: {
        ...prev.patient,
        [name]: value,
      },
    }));
  }, []);

  const handleValidatedChange = useCallback((examTypeId, e) => {
    const checked = e.target.checked;
    setFormData((prev) => {
      const newTests = {
        ...prev.tests,
        [examTypeId]: {
          ...prev.tests[examTypeId],
          validated: checked,
        },
      };

      // Check if all exam types are validated
      const allValidated = Object.values(newTests).every(
        (test) => test.validated === true
      );

      return {
        ...prev,
        tests: newTests,
        allValidated: allValidated,
      };
    });
  }, []);

  const [prosecingSearchPatient, setProsecingSearchPatient] = useState(false);
  const searchPatient = debounce(async (ci) => {
    setProsecingSearchPatient(true); // Cambiar a verdadero antes de la búsqueda

    try {
      const res = await externalApi.get(`/patients?ci=${ci}`);
      if (res.data.data.data.length === 0) {
        console.log("No se encontró el paciente");
        setFormData((prev) => ({
          ...prev,
          patient: {
            ...prev.patient,
            patient_id: null,
          },
        }));
        return;
      } else {
        console.log(res.data.data.data[0], "Se encontró el paciente");
        setFormData((prev) => ({
          ...prev,
          patient: {
            ...prev.patient,
            first_name: res.data.data.data[0]?.name,
            ...res.data.data.data[0],
            patient_id: res.data.data.data[0]?.id,
          },
        }));
      }
    } catch (err) {
      console.log(err);
    } finally {
      setProsecingSearchPatient(false); // Cambiar a falso después de la búsqueda
    }
  }, 280);

  return (
    <div style={{ height: 580, width: "100%" }}>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Exámenes médicos</h1>
        <FuturisticButton
          onClick={() => {
            setIsModalOpen(true);
            if (submitString === "Actualizar") {
              setSubmitString("Registrar");
              setFormData(structuredClone(defaultFormData));
            }
          }}
        >
          Registrar exámen
        </FuturisticButton>
      </div>
      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
        title="Registrar exámen"
        size="xl"
      >
        <form
          className={`grid grid-cols-2 gap-7 w-full relative`}
          onSubmit={onSubmit}
        >
          <div className="space-y-3 z-10">
            <h2 className="text-xl font-bold mb-2">Información del Paciente</h2>
            <div className="grid grid-cols-2 gap-4">
              {formData?.patient?.ci.length >= 6 && (
                <div className="w-full col-span-2 h-6 overflow-hidden text-center">
                  {prosecingSearchPatient ? (
                    <Icon
                      icon="eos-icons:three-dots-loading"
                      className="text-3xl"
                    />
                  ) : formData?.patient.patient_id !== null ? (
                    <span className="flex items-center gap-2 text-center mx-auto justify-center">
                      <Icon
                        icon="iconoir:settings-profiles"
                        className="text-2xl text-color3"
                      />
                      <small>Paciente Registrado con historia</small>
                    </span>
                  ) : (
                    <span className="flex items-center gap-2 text-center mx-auto justify-center">
                      <Icon icon="clarity:new-line" className="text-3xl" />
                      <small>Nuevo paciente sin historia</small>
                    </span>
                  )}
                </div>
              )}
              {patientFormFields.map((field) => {
                if (field.name === "ci") {
                  return (
                    <div key={field.name}>
                      <FormField
                        {...field}
                        value={formData.patient?.[field.name]}
                        onPaste={(e) => {
                          // Manejar pegado específicamente
                          const pastedValue = e.clipboardData.getData("text");
                          formData.patient.patient_id = null;

                          // Actualizar el valor del campo
                          const syntheticEvent = {
                            target: {
                              name: field.name,
                              value: pastedValue,
                            },
                          };

                          handlePatientInputChange(syntheticEvent);

                          if (pastedValue.length >= 6) {
                            setProsecingSearchPatient(true);
                            searchPatient(pastedValue);
                          }
                        }}
                        onInput={(e) => {
                          formData.patient.patient_id = null;
                          handlePatientInputChange(e);
                          if (formData.patient.ci.length >= 6) {
                            setProsecingSearchPatient(true);
                            searchPatient(e.target.value);
                          }
                        }}
                      />
                    </div>
                  );
                } else {
                  return (
                    <FormField
                      key={field.name}
                      {...field}
                      value={formData.patient?.[field.name]}
                      onChange={handlePatientInputChange}
                    />
                  );
                }
              })}
            </div>
          </div>
          <div className="space-y-3 z-10 ">
            <h2 className="text-xl font-bold">Resultados del Exámen</h2>

            {Object.entries(formData.tests || {}).length === 0 ? (
              <p>Seleccione al menos un tipo de exámen</p>
            ) : (
              Object.keys(formData?.tests || {})?.map((key) => (
                <div
                  key={key}
                  className="bg-color4 p-3 rounded-xl shadow-md mb-1 bg-opacity-5"
                >
                  <div className="flex justify-between items-center ">
                    <h3 className="text-lg font-bold text-color1 mb-4">
                      {formData.tests[key]?.testTypeName}
                    </h3>
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-500 focus:outline-none"
                      aria-label="Close"
                      onClick={() => {
                        setFormData((prev) => {
                          const { [key]: value, ...rest } = prev.tests;

                          return {
                            ...prev,
                            tests: rest,
                          };
                        });
                      }}
                    >
                      <span className="sr-only">Close</span>
                      <svg
                        className="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(formData.tests[key]?.testValues || {})?.map(
                      ([name, field]) => (
                        <MemoizedTestField
                          key={name}
                          field={field}
                          value={formData.tests[key]?.testValues?.[name]?.value}
                          onChange={handleTestInputChange}
                          testKey={key}
                          fieldName={name}
                        />
                      )
                    )}
                    <div className="ml-auto col-span-2 flex items-center gap-3">
                      <input
                        type="checkbox"
                        name={`validated-${key}`}
                        onChange={(e) => handleValidatedChange(key, e)}
                        checked={formData.tests[key]?.validated || false}
                        id={`validated-${key}`}
                      />
                      <label htmlFor={`validated-${key}`}>Validado</label>
                    </div>
                  </div>
                </div>
              ))
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {examinationTypes.map((examType) => {
                if (formData.tests[examType.id]) {
                  return null;
                }
                return (
                  <button
                    type="button"
                    key={examType.id}
                    className="hover bg-gray-200 py-5 hover:bg-gray-300 rounded "
                    onClick={() => {
                      const newtestValues = examType.tests.reduce(
                        (acc, test) => {
                          acc[test.name] = {
                            ...test,
                            value: "", // Add empty value field
                          };
                          return acc;
                        },
                        {}
                      );
                      setFormData((prev) => ({
                        ...prev,
                        allValidated: false,
                        tests: {
                          [examType.id]: {
                            testValues: newtestValues,
                            testTypeName: examType.name,
                            testTypeId: examType.id,
                            validated: false,
                          },
                          ...prev.tests,
                        },
                      }));
                      setTimeout(() => {
                        document
                          .querySelector(
                            `input[name=${examType.tests[0].name}]`
                          )
                          .focus();
                      }, 120);
                    }}
                  >
                    {examType.name}
                  </button>
                );
              })}
            </div>
          </div>
          <div className="col-span-2">
            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="submit"
                variant="contained"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : null}
                className={`px-16 py-3 rounded-md font-semibold ${
                  loading ? "opacity-50 cursor-not-allowed" : ""
                } ${
                  submitString == "Actualizar"
                    ? "bg-color4 text-color1"
                    : "bg-color1 text-color4"
                }`}
              >
                {loading ? "Procesando..." : submitString}
              </button>
            </div>
          </div>
        </form>
      </Modal>
      <div
        className="ag-theme-alpine ag-grid-no-border"
        style={{ height: 500 }}
      >
        {
          <MaterialReactTable
            columns={columns}
            data={data}
            rowCount={rowCount}
            manualPagination
            initialState={{ density: "compact" }}
            manualSorting
            manualFiltering
            state={{ pagination, sorting, columnFilters, isLoading }}
            onPaginationChange={setPagination}
            onSortingChange={setSorting}
            onColumnFiltersChange={setColumnFilters}
            muiTablePaginationProps={{
              rowsPerPageOptions: [10, 25, 50, 100],
              showFirstButton: true,
              showLastButton: true,
            }}
          />
        }
      </div>
    </div>
  );
}
