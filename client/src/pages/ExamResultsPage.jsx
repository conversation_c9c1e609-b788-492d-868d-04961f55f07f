import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { examResultsAPI } from '../services/api';

export default function ExamResultsPage() {
  const { token } = useParams();
  const [examData, setExamData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchExamResults = async () => {
      try {
        setLoading(true);
        const response = await examResultsAPI.getByToken(token);
        setExamData(response.data);
      } catch (err) {
        setError(err.message || 'Error al cargar los resultados');
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      fetchExamResults();
    }
  }, [token]);

  const downloadPDF = async () => {
    try {
      const response = await examResultsAPI.downloadPDF(token);
      // Create blob and download
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `resultados-${examData.patient.ci}.pdf`;
      link.click();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading PDF:', err);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando resultados...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Error</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <p className="text-sm text-gray-500">
            El enlace puede haber expirado o ser inválido.
          </p>
        </div>
      </div>
    );
  }

  if (!examData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <p className="text-gray-600">No se encontraron resultados.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                Resultados de Laboratorio
              </h1>
              <div className="text-gray-600">
                <p><strong>Paciente:</strong> {examData.patient.first_name} {examData.patient.last_name}</p>
                <p><strong>C.I:</strong> {examData.patient.ci}</p>
                <p><strong>Fecha:</strong> {examData.created_date}</p>
              </div>
            </div>
            <div className="text-right">
              <img src="/logo-labfalcon.png" alt="Lab Falcon" className="h-16 mb-2" />
              <p className="text-sm text-gray-500">Laboratorio Falcón</p>
            </div>
          </div>
        </div>

        {/* Results Content */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Resultados de Exámenes</h2>
          
          {Object.entries(examData.tests).map(([testId, test]) => (
            <div key={testId} className="mb-6 p-4 border border-gray-200 rounded-lg">
              <h3 className="text-lg font-medium text-blue-600 mb-3">
                {test.testTypeName}
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(test.testValues).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-gray-700">{key}:</span>
                    <span className="text-gray-900">{value}</span>
                  </div>
                ))}
              </div>
              
              <div className="mt-3 flex items-center">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  test.validated 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {test.validated ? '✓ Validado' : '⏳ Pendiente'}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <button
            onClick={downloadPDF}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 inline-flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Descargar PDF
          </button>
          
          <p className="text-sm text-gray-500 mt-4">
            Este enlace es válido por tiempo limitado por motivos de seguridad.
          </p>
        </div>
      </div>
    </div>
  );
}
